const axios = require('axios');

async function testStudentsAPI() {
  try {
    console.log('Testing students API endpoint...');
    const response = await axios.get('http://localhost:5000/api/students');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testStudentsAPI();
